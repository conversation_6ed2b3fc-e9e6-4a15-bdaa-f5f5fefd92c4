const fs = require('fs');
const path = require('path');

console.log('🧹 清理不必要的依赖文件...\n');

// 需要清理的文件和目录模式
const cleanupPatterns = [
  // 测试文件
  '**/test/**',
  '**/tests/**',
  '**/__tests__/**',
  '**/*.test.js',
  '**/*.spec.js',
  
  // 文档文件
  '**/README*',
  '**/CHANGELOG*',
  '**/LICENSE*',
  '**/HISTORY*',
  '**/*.md',
  '**/docs/**',
  '**/doc/**',
  '**/examples/**',
  '**/example/**',
  
  // 配置文件
  '**/.eslintrc*',
  '**/.babelrc*',
  '**/tsconfig.json',
  '**/.editorconfig',
  '**/.gitignore',
  '**/.npmignore',
  
  // 源码映射
  '**/*.map',
  
  // TypeScript 定义文件 (保留必要的)
  '**/*.d.ts',
  
  // 构建工具
  '**/Gruntfile.js',
  '**/Gulpfile.js',
  '**/Makefile',
  
  // 缓存和临时文件
  '**/.cache/**',
  '**/tmp/**',
  '**/temp/**',
  
  // 编辑器文件
  '**/.vscode/**',
  '**/.idea/**',
  '**/*.swp',
  '**/*.swo',
  
  // 日志文件
  '**/*.log',
  '**/logs/**'
];

function shouldCleanFile(filePath) {
  const fileName = path.basename(filePath);
  const dirName = path.dirname(filePath);
  
  // 检查文件名模式
  if (fileName.match(/^(README|CHANGELOG|LICENSE|HISTORY|AUTHORS|NOTICE)/i)) return true;
  if (fileName.endsWith('.md') || fileName.endsWith('.txt')) return true;
  if (fileName.endsWith('.map')) return true;
  if (fileName.match(/\.(test|spec)\.(js|ts)$/)) return true;
  if (fileName.match(/^\.eslintrc|^\.babelrc|^\.editorconfig|^\.gitignore|^\.npmignore/)) return true;
  
  // 检查目录模式
  if (dirName.includes('/test/') || dirName.includes('/tests/') || dirName.includes('/__tests__/')) return true;
  if (dirName.includes('/docs/') || dirName.includes('/doc/') || dirName.includes('/examples/') || dirName.includes('/example/')) return true;
  if (dirName.includes('/.cache/') || dirName.includes('/tmp/') || dirName.includes('/temp/')) return true;
  if (dirName.includes('/.vscode/') || dirName.includes('/.idea/')) return true;
  if (dirName.includes('/logs/') || dirName.includes('/coverage/')) return true;
  
  return false;
}

function cleanupDirectory(dir) {
  if (!fs.existsSync(dir)) return;
  
  let cleanedFiles = 0;
  let savedSpace = 0;
  
  function walkDir(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const itemPath = path.join(currentDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // 递归处理子目录
        walkDir(itemPath);
        
        // 检查目录是否为空，如果为空则删除
        try {
          const dirItems = fs.readdirSync(itemPath);
          if (dirItems.length === 0) {
            fs.rmdirSync(itemPath);
            console.log(`  🗑️ 删除空目录: ${path.relative(dir, itemPath)}`);
          }
        } catch (err) {
          // 忽略错误
        }
      } else {
        // 检查文件是否需要清理
        if (shouldCleanFile(itemPath)) {
          try {
            savedSpace += stat.size;
            fs.unlinkSync(itemPath);
            cleanedFiles++;
            console.log(`  🗑️ 删除文件: ${path.relative(dir, itemPath)}`);
          } catch (err) {
            console.log(`  ⚠️ 无法删除: ${path.relative(dir, itemPath)} - ${err.message}`);
          }
        }
      }
    }
  }
  
  walkDir(dir);
  
  return { cleanedFiles, savedSpace };
}

// 清理 app/node_modules
const appNodeModules = 'app/node_modules';
if (fs.existsSync(appNodeModules)) {
  console.log('🧹 清理 app/node_modules...');
  const result = cleanupDirectory(appNodeModules);
  console.log(`✅ 清理完成: 删除 ${result.cleanedFiles} 个文件，节省 ${(result.savedSpace / 1024 / 1024).toFixed(2)} MB\n`);
} else {
  console.log('⚠️ app/node_modules 不存在，跳过清理\n');
}

// 清理根目录的 node_modules (可选)
const rootNodeModules = 'node_modules';
if (process.argv.includes('--clean-root') && fs.existsSync(rootNodeModules)) {
  console.log('🧹 清理根目录 node_modules...');
  const result = cleanupDirectory(rootNodeModules);
  console.log(`✅ 清理完成: 删除 ${result.cleanedFiles} 个文件，节省 ${(result.savedSpace / 1024 / 1024).toFixed(2)} MB\n`);
}

console.log('🎉 依赖清理完成！');
console.log('\n💡 使用说明:');
console.log('- 运行 "node cleanup-dependencies.js" 清理 app/node_modules');
console.log('- 运行 "node cleanup-dependencies.js --clean-root" 同时清理根目录 node_modules');
console.log('- 这将显著减少打包后的应用体积');

const express = require('express');
const router = express.Router();
const { isAdmin } = require('../middleware/auth');

// Get database connection - works in both standalone and Electron environments
function getDb() {
  if (global.getDb) {
    // Electron environment
    return global.getDb();
  } else {
    // Standalone server environment
    const { getDb: localGetDb } = require('../db/init');
    return localGetDb();
  }
}

// 获取所有商品
router.get('/', (req, res) => {
  try {
    const db = getDb();
    const { categoryId, includeUnavailable } = req.query;
    
    // 默认只获取可用商品，除非明确指定includeUnavailable=true
    const availabilityFilter = includeUnavailable === 'true' ? '' : 'AND p.is_available = 1';
    
    let products = [];
    
    if (categoryId) {
      // 获取指定分类的商品
      products = db.prepare(`
        SELECT p.*, c.name as category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        WHERE p.category_id = ? ${availabilityFilter}
        ORDER BY p.name
      `).all(categoryId);
    } else {
      // 获取所有商品
      products = db.prepare(`
        SELECT p.*, c.name as category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        ${availabilityFilter ? 'WHERE ' + availabilityFilter.substring(4) : ''}
        ORDER BY p.name
      `).all();
    }
    
    // 获取每个商品的规格
    const options = db.prepare(`
      SELECT * FROM product_options
      WHERE product_id = ?
    `);
    
    // 格式化商品数据
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      categoryId: product.category_id,
      categoryName: product.category_name,
      description: product.description,
      image: product.image,
      isAvailable: product.is_available === 1,
      options: options.all(product.id).map(option => ({
        id: option.id,
        name: option.name,
        price: option.price,
        isDefault: option.is_default === 1
      })),
      createdAt: product.created_at,
      updatedAt: product.updated_at
    }));
    
    res.json({
      success: true,
      data: formattedProducts
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取商品失败'
    });
  }
});

// 获取单个商品
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const db = getDb();
    
    // 获取商品基本信息
    const product = db.prepare(`
      SELECT p.*, c.name as category_name
      FROM products p
      JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `).get(id);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }
    
    // 获取商品规格
    const options = db.prepare(`
      SELECT * FROM product_options
      WHERE product_id = ?
    `).all(id);
    
    // 格式化商品数据
    const formattedProduct = {
      id: product.id,
      name: product.name,
      categoryId: product.category_id,
      categoryName: product.category_name,
      description: product.description,
      image: product.image,
      isAvailable: product.is_available === 1,
      options: options.map(option => ({
        id: option.id,
        name: option.name,
        price: option.price,
        isDefault: option.is_default === 1
      })),
      createdAt: product.created_at,
      updatedAt: product.updated_at
    };
    
    res.json({
      success: true,
      data: formattedProduct
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取商品详情失败'
    });
  }
});

// 创建商品 - 仅管理员
router.post('/', isAdmin, (req, res) => {
  try {
    const {
      name,
      categoryId,
      description,
      image,
      options,
      isAvailable
    } = req.body;
    
    // 验证必填字段
    if (!name || !categoryId || !options || options.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必要字段'
      });
    }
    
    const db = getDb();
    
    // 开始事务
    db.prepare('BEGIN TRANSACTION').run();
    
    try {
      // 插入商品基本信息
      const result = db.prepare(`
        INSERT INTO products
        (name, category_id, description, image, is_available)
        VALUES (?, ?, ?, ?, ?)
      `).run(
        name,
        categoryId,
        description || null,
        image || null,
        isAvailable ? 1 : 0
      );
      
      const productId = result.lastInsertRowid;
      
      // 插入商品规格
      const insertOption = db.prepare(`
        INSERT INTO product_options
        (product_id, name, price, is_default)
        VALUES (?, ?, ?, ?)
      `);
      
      options.forEach(option => {
        insertOption.run(
          productId,
          option.name,
          option.price,
          option.isDefault ? 1 : 0
        );
      });
      
      // 提交事务
      db.prepare('COMMIT').run();
      
      // 获取新创建的商品数据
      const newProduct = db.prepare(`
        SELECT p.*, c.name as category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        WHERE p.id = ?
      `).get(productId);
      
      // 获取商品规格
      const newOptions = db.prepare(`
        SELECT * FROM product_options
        WHERE product_id = ?
      `).all(productId);
      
      // 格式化商品数据
      const formattedProduct = {
        id: newProduct.id,
        name: newProduct.name,
        categoryId: newProduct.category_id,
        categoryName: newProduct.category_name,
        description: newProduct.description,
        image: newProduct.image,
        isAvailable: newProduct.is_available === 1,
        options: newOptions.map(option => ({
          id: option.id,
          name: option.name,
          price: option.price,
          isDefault: option.is_default === 1
        })),
        createdAt: newProduct.created_at,
        updatedAt: newProduct.updated_at
      };
      
      res.status(201).json({
        success: true,
        data: formattedProduct
      });
    } catch (error) {
      // 回滚事务
      db.prepare('ROLLBACK').run();
      throw error;
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建商品失败'
    });
  }
});

// 更新商品 - 仅管理员
router.put('/:id', isAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      categoryId,
      description,
      image,
      options,
      isAvailable
    } = req.body;
    
    // 验证必填字段
    if (!name || !categoryId || !options || options.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必要字段'
      });
    }
    
    const db = getDb();
    
    // 检查商品是否存在
    const existingProduct = db.prepare('SELECT id FROM products WHERE id = ?').get(id);
    
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }
    
    // 开始事务
    db.prepare('BEGIN TRANSACTION').run();
    
    try {
      // 更新商品基本信息
      db.prepare(`
        UPDATE products SET
        name = ?,
        category_id = ?,
        description = ?,
        image = ?,
        is_available = ?,
        updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(
        name,
        categoryId,
        description || null,
        image || null,
        isAvailable ? 1 : 0,
        id
      );
      
      // 删除原有规格
      db.prepare('DELETE FROM product_options WHERE product_id = ?').run(id);
      
      // 插入新规格
      const insertOption = db.prepare(`
        INSERT INTO product_options
        (product_id, name, price, is_default)
        VALUES (?, ?, ?, ?)
      `);
      
      options.forEach(option => {
        insertOption.run(
          id,
          option.name,
          option.price,
          option.isDefault ? 1 : 0
        );
      });
      
      // 提交事务
      db.prepare('COMMIT').run();
      
      // 获取更新后的商品数据
      const updatedProduct = db.prepare(`
        SELECT p.*, c.name as category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        WHERE p.id = ?
      `).get(id);
      
      // 获取商品规格
      const updatedOptions = db.prepare(`
        SELECT * FROM product_options
        WHERE product_id = ?
      `).all(id);
      
      // 格式化商品数据
      const formattedProduct = {
        id: updatedProduct.id,
        name: updatedProduct.name,
        categoryId: updatedProduct.category_id,
        categoryName: updatedProduct.category_name,
        description: updatedProduct.description,
        image: updatedProduct.image,
        isAvailable: updatedProduct.is_available === 1,
        options: updatedOptions.map(option => ({
          id: option.id,
          name: option.name,
          price: option.price,
          isDefault: option.is_default === 1
        })),
        createdAt: updatedProduct.created_at,
        updatedAt: updatedProduct.updated_at
      };
      
      res.json({
        success: true,
        data: formattedProduct
      });
    } catch (error) {
      // 回滚事务
      db.prepare('ROLLBACK').run();
      throw error;
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新商品失败'
    });
  }
});

// 删除商品 - 仅管理员
router.delete('/:id', isAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const { forceSoftDelete } = req.query; // 是否强制软删除
    const db = getDb();
    
    // 检查商品是否存在
    const existingProduct = db.prepare('SELECT id, name FROM products WHERE id = ?').get(id);
    
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }
    
    // 开始事务
    db.prepare('BEGIN TRANSACTION').run();
    
    try {
      // 检查商品是否在订单中被使用
      const orderItemCount = db.prepare(
        'SELECT COUNT(*) as count FROM order_items WHERE product_id = ?'
      ).get(id);
      
      // 如果商品在订单中被使用
      if (orderItemCount.count > 0) {
        // 如果强制软删除
        if (forceSoftDelete === 'true') {
          // 软删除：将商品标记为不可用
          db.prepare(`
            UPDATE products SET
            is_available = 0,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(id);
          
          db.prepare('COMMIT').run();
          
          return res.json({
            success: true,
            message: `商品"${existingProduct.name}"已被标记为不可用`,
            softDelete: true
          });
        } else {
          // 回滚事务，返回错误信息
          db.prepare('ROLLBACK').run();
          
          return res.status(409).json({
            success: false,
            message: `无法删除商品"${existingProduct.name}"，因为它已在订单中被使用`,
            inUse: true,
            usageCount: orderItemCount.count
          });
        }
      }
      
      // 如果商品没有在订单中被使用，执行物理删除
      // 删除商品规格
      db.prepare('DELETE FROM product_options WHERE product_id = ?').run(id);
      
      // 删除商品
      db.prepare('DELETE FROM products WHERE id = ?').run(id);
      
      // 提交事务
      db.prepare('COMMIT').run();
      
      res.json({
        success: true,
        message: '商品删除成功'
      });
    } catch (error) {
      // 回滚事务
      db.prepare('ROLLBACK').run();
      throw error;
    }
  } catch (error) {
    console.error('删除商品错误:', error);
    res.status(500).json({
      success: false,
      message: '删除商品失败'
    });
  }
});

module.exports = router; 
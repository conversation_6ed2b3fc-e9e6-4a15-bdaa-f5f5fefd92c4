@echo off
title Shop Desktop - 商店管理系统
echo.
echo ========================================
echo    Shop Desktop - 商店管理系统
echo ========================================
echo.
echo 正在启动应用程序...
echo.

REM 检查是否存在主程序
if not exist "Shop Desktop.exe" (
    echo 错误: 未找到主程序文件
    echo 请确保所有文件完整
    pause
    exit /b 1
)

REM 启动应用程序
start "" "Shop Desktop.exe"

REM 等待一下确保程序启动
timeout /t 2 /nobreak >nul

echo 应用程序已启动！
echo.
echo 如果程序没有正常启动，请：
echo 1. 检查是否有杀毒软件阻止
echo 2. 以管理员身份运行此脚本
echo 3. 确保系统满足最低要求 (Windows 10 x64)
echo.
pause

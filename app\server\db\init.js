const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const DB_PATH = path.join(__dirname, 'shop.db');

// 全局数据库连接
let db = null;

/**
 * 初始化数据库
 */
function initDatabase() {
  return new Promise((resolve, reject) => {
    try {
      // 检查数据库文件是否存在
      const dbExists = fs.existsSync(DB_PATH);
      
      // 创建数据库连接
      db = new Database(DB_PATH);
      
      // 读取数据库初始化SQL脚本
      const schemaPath = path.join(__dirname, 'schema.sql');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      // 执行SQL脚本
      db.exec(schema);
      
      // 初始化默认用户（如果不存在）
      initDefaultUsers();
      
      // 如果是新数据库，导入初始数据
      if (!dbExists) {
        importInitialData();
      }
      
      console.log('数据库初始化完成');
      
      resolve(db);
    } catch (err) {
      reject(err);
    }
  });
}

/**
 * 初始化默认用户
 */
function initDefaultUsers() {
  const usersExist = db.prepare('SELECT COUNT(*) as count FROM users').get();
  
  if (usersExist.count === 0) {
    // 创建默认管理员用户
    db.prepare(
      'INSERT INTO users (username, password, role) VALUES (?, ?, ?)'
    ).run('admin', 'admin123', 'admin');
    
    // 创建默认收银员用户
    db.prepare(
      'INSERT INTO users (username, password, role) VALUES (?, ?, ?)'
    ).run('cashier', 'cashier123', 'cashier');
    
    console.log('创建了默认用户');
  }
}

/**
 * 导入初始示例数据
 */
function importInitialData() {
  try {
    const initialDataPath = path.join(__dirname, 'initial-data.sql');
    const initialData = fs.readFileSync(initialDataPath, 'utf8');
    
    // 执行初始数据脚本
    db.exec(initialData);
    
    console.log('导入初始数据成功');
  } catch (err) {
    console.error('导入初始数据失败');
  }
}

/**
 * 获取数据库连接
 */
function getDb() {
  if (!db) {
    throw new Error('数据库未初始化');
  }
  return db;
}

module.exports = {
  initDatabase,
  getDb,
}; 
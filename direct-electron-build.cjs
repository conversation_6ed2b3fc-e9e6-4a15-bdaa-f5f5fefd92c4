const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 直接 Electron 优化构建...\n');

try {
  // 1. 强制终止相关进程
  console.log('🔄 终止相关进程...');
  try {
    execSync('taskkill /f /im "Shop Desktop.exe" 2>nul', { stdio: 'ignore' });
    execSync('taskkill /f /im "electron.exe" 2>nul', { stdio: 'ignore' });
    execSync('taskkill /f /im "app-builder.exe" 2>nul', { stdio: 'ignore' });
    console.log('✅ 进程清理完成');
  } catch (error) {
    console.log('⚠️ 进程清理警告 (可忽略)');
  }

  // 等待文件释放
  console.log('⏳ 等待文件释放...');
  const start = Date.now();
  while (Date.now() - start < 3000) {
    // 等待 3 秒
  }

  // 2. 清理构建目录 (保留 dist)
  console.log('\n🧹 清理构建目录...');
  try {
    if (fs.existsSync('dist-electron')) {
      execSync('powershell -Command "Remove-Item -Recurse -Force dist-electron -ErrorAction SilentlyContinue"', { stdio: 'inherit' });
    }
    if (fs.existsSync('app')) {
      execSync('powershell -Command "Remove-Item -Recurse -Force app -ErrorAction SilentlyContinue"', { stdio: 'inherit' });
    }
    console.log('✅ 目录清理完成 (保留 dist)');
  } catch (error) {
    console.log('⚠️ 清理警告:', error.message);
  }

  // 3. 检查现有构建
  console.log('\n🔍 检查现有构建...');
  if (!fs.existsSync('dist')) {
    console.log('❌ 未找到 dist 目录，请先运行: pnpm run build');
    process.exit(1);
  }
  console.log('✅ 找到现有前端构建');

  // 4. 创建优化的 app 目录结构
  console.log('\n📁 创建优化的 app 目录结构...');
  const dirs = [
    'app',
    'app/dist',
    'app/electron',
    'app/server',
    'app/server/db',
    'app/server/routes',
    'app/server/middleware'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`  ✓ 创建 ${dir}`);
    }
  });

  // 5. 创建优化的 app/package.json
  console.log('\n📄 创建优化的 app/package.json...');
  const appPackageJson = {
    "name": "shop-desktop",
    "version": "1.0.0",
    "description": "Shop Desktop - 商店桌面管理系统",
    "author": "Shop Desktop Team",
    "main": "electron/main.js",
    "type": "commonjs",
    "dependencies": {
      "better-sqlite3": "^11.10.0",
      "body-parser": "^2.2.0",
      "cors": "^2.8.5",
      "express": "^4.21.2",
      "jsonwebtoken": "^9.0.2"
    }
  };
  
  fs.writeFileSync('app/package.json', JSON.stringify(appPackageJson, null, 2));
  console.log('✅ app/package.json 创建完成');

  // 6. 复制文件
  console.log('\n📦 复制文件...');
  
  // 复制前端构建文件
  console.log('  📋 复制前端文件...');
  execSync('xcopy dist app\\dist /E /I /H /Y /Q', { stdio: 'pipe' });
  console.log('  ✅ 前端文件复制完成');
  
  // 复制 Electron 文件
  console.log('  ⚡ 复制 Electron 文件...');
  const electronFiles = [
    'electron/main.js',
    'electron/server.js',
    'electron/db-init.js'
  ];
  
  electronFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join('app', file);
      fs.copyFileSync(file, destFile);
      console.log(`    ✓ ${file}`);
    }
  });
  
  // 复制服务器文件
  console.log('  🖥️ 复制服务器文件...');
  const serverFiles = [
    'server/index.js',
    'server/db/init.js',
    'server/db/schema.sql',
    'server/db/initial-data.sql',
    'server/routes/auth.js',
    'server/routes/products.js',
    'server/routes/categories.js',
    'server/routes/orders.js',
    'server/routes/reports.js',
    'server/middleware/auth.js',
    'server/middleware/logger.js'
  ];
  
  serverFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join('app', file);
      fs.copyFileSync(file, destFile);
      console.log(`    ✓ ${file}`);
    }
  });

  // 7. 在 app 目录安装生产依赖
  console.log('\n📦 安装生产依赖...');
  const originalDir = process.cwd();
  process.chdir('app');
  
  try {
    execSync('pnpm install --prod --silent', { stdio: 'inherit' });
    console.log('✅ 生产依赖安装完成');
  } catch (error) {
    console.log('⚠️ pnpm 安装失败，尝试使用 npm...');
    execSync('npm install --production --silent', { stdio: 'inherit' });
    console.log('✅ 生产依赖安装完成 (npm)');
  }
  
  process.chdir(originalDir);

  // 8. 直接使用 electron-builder 构建 (不调用其他脚本)
  console.log('\n🔧 直接使用 electron-builder 构建...');
  
  // 备份原始配置
  const rootPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const originalBuildConfig = JSON.parse(JSON.stringify(rootPackageJson.build));
  
  // 修改为使用 app 目录
  rootPackageJson.build.directories = {
    ...rootPackageJson.build.directories,
    app: 'app'
  };
  
  // 简化构建配置，避免复杂依赖
  rootPackageJson.build.files = [
    "dist/**/*",
    "electron/**/*", 
    "server/**/*",
    "!**/*.map",
    "!**/*.md"
  ];
  
  // 写入临时配置
  fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2));
  
  try {
    // 直接使用 electron-builder，指定平台和架构
    console.log('📦 运行 electron-builder (Windows x64)...');
    execSync('npx electron-builder --win --x64 --config.compression=normal', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        ELECTRON_CACHE: './electron-cache'
      }
    });
    console.log('✅ 构建完成');
  } catch (error) {
    console.log('⚠️ 构建失败，恢复原始配置');
    // 恢复原始配置
    rootPackageJson.build = originalBuildConfig;
    fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2));
    throw error;
  }
  
  // 恢复原始配置
  rootPackageJson.build = originalBuildConfig;
  fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2));

  // 9. 显示结果
  console.log('\n📊 构建结果:');
  if (fs.existsSync('dist-electron')) {
    const files = fs.readdirSync('dist-electron');
    files.forEach(file => {
      if (file.endsWith('.exe')) {
        const filePath = path.join('dist-electron', file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`  ✅ ${file} (${sizeInMB} MB)`);
      }
    });
  }

  // 10. 显示 app.asar 大小对比
  const appAsarPath = 'dist-electron/win-unpacked/resources/app.asar';
  if (fs.existsSync(appAsarPath)) {
    const stats = fs.statSync(appAsarPath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`  📦 app.asar: ${sizeInMB} MB (优化后)`);
    
    // 计算优化效果
    console.log('\n💡 优化效果:');
    console.log(`- 📦 app.asar 大小: ${sizeInMB} MB`);
    console.log('- ✅ 使用双 package.json 结构');
    console.log('- ✅ 只打包运行时必需的依赖 (5个)');
    console.log('- ✅ 排除了所有前端开发依赖 (57个)');
    console.log('- ✅ 显著减少了应用体积');
    
    // 与之前的版本对比
    const previousSize = 416.97; // 之前的版本大小
    const optimizedSize = parseFloat(sizeInMB);
    if (optimizedSize < previousSize) {
      const reduction = ((previousSize - optimizedSize) / previousSize * 100).toFixed(1);
      console.log(`- 📉 相比之前版本减少: ${reduction}%`);
    }
  }

  // 11. 分析 app 目录大小
  console.log('\n📁 app 目录分析:');
  if (fs.existsSync('app')) {
    let totalSize = 0;
    let fileCount = 0;
    
    function calculateDirSize(dir) {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const itemPath = path.join(dir, item);
        try {
          const stat = fs.statSync(itemPath);
          if (stat.isDirectory()) {
            calculateDirSize(itemPath);
          } else {
            totalSize += stat.size;
            fileCount++;
          }
        } catch (err) {
          // 忽略权限错误
        }
      }
    }
    
    calculateDirSize('app');
    const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
    console.log(`  📦 app 目录总大小: ${sizeInMB} MB (${fileCount} 个文件)`);
    
    // 分析 node_modules
    if (fs.existsSync('app/node_modules')) {
      const nodeModulesItems = fs.readdirSync('app/node_modules');
      console.log(`  📚 生产依赖包数量: ${nodeModulesItems.length} 个`);
    }
  }

  console.log('\n🎉 直接优化构建完成！');
  console.log('\n📋 构建产物:');
  console.log('- 📁 dist-electron/win-unpacked/ - 可执行文件');
  console.log('- 📦 dist-electron/*.exe - 安装程序');
  console.log('- 🗂️ app/ - 优化后的源码结构');
  console.log('\n🚀 现在可以分发给用户了！');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}

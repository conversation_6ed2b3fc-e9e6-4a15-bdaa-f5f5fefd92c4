const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始优化构建流程...\n');

// 1. 清理旧的构建文件
console.log('🧹 清理旧构建...');
const cleanDirs = ['app/dist', 'app/electron', 'app/server', 'dist-electron'];
cleanDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    fs.rmSync(dir, { recursive: true, force: true });
    console.log(`  ✓ 清理 ${dir}`);
  }
});

// 2. 构建前端
console.log('\n🔨 构建前端...');
try {
  execSync('pnpm run build', { stdio: 'inherit' });
  console.log('✅ 前端构建完成');
} catch (error) {
  console.error('❌ 前端构建失败:', error.message);
  process.exit(1);
}

// 3. 创建 app 目录结构
console.log('\n📁 创建 app 目录结构...');
const appDir = 'app';
const dirs = [
  path.join(appDir, 'dist'),
  path.join(appDir, 'electron'),
  path.join(appDir, 'server'),
  path.join(appDir, 'server/db'),
  path.join(appDir, 'server/routes'),
  path.join(appDir, 'server/middleware')
];

dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`  ✓ 创建 ${dir}`);
  }
});

// 4. 复制构建后的前端文件
console.log('\n📦 复制前端构建文件...');
if (fs.existsSync('dist')) {
  fs.cpSync('dist', path.join(appDir, 'dist'), { recursive: true });
  console.log('  ✓ 复制前端文件');
}

// 5. 复制 Electron 文件
console.log('\n⚡ 复制 Electron 文件...');
const electronFiles = [
  'electron/main.js',
  'electron/server.js',
  'electron/db-init.js'
];

electronFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const destFile = path.join(appDir, file);
    fs.copyFileSync(file, destFile);
    console.log(`  ✓ 复制 ${file}`);
  }
});

// 6. 复制服务器文件
console.log('\n🖥️ 复制服务器文件...');
const serverFiles = [
  'server/index.js',
  'server/db/init.js',
  'server/db/schema.sql',
  'server/db/initial-data.sql',
  'server/routes/auth.js',
  'server/routes/products.js',
  'server/routes/categories.js',
  'server/routes/orders.js',
  'server/routes/reports.js',
  'server/middleware/auth.js',
  'server/middleware/logger.js'
];

serverFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const destFile = path.join(appDir, file);
    fs.copyFileSync(file, destFile);
    console.log(`  ✓ 复制 ${file}`);
  }
});

// 7. 在 app 目录中安装生产依赖
console.log('\n📦 安装生产依赖...');
try {
  process.chdir(appDir);
  execSync('pnpm install --prod', { stdio: 'inherit' });
  console.log('✅ 生产依赖安装完成');
  process.chdir('..');
} catch (error) {
  console.error('❌ 依赖安装失败:', error.message);
  process.chdir('..');
  process.exit(1);
}

// 8. 运行 electron-builder
console.log('\n🔧 运行 electron-builder...');
try {
  execSync('pnpm electron-builder', { stdio: 'inherit' });
  console.log('✅ Electron 应用构建完成');
} catch (error) {
  console.error('❌ Electron 构建失败:', error.message);
  process.exit(1);
}

// 9. 显示构建结果
console.log('\n📊 构建结果:');
if (fs.existsSync('dist-electron')) {
  const files = fs.readdirSync('dist-electron');
  files.forEach(file => {
    if (file.endsWith('.exe') || file.endsWith('.dmg') || file.endsWith('.AppImage')) {
      const filePath = path.join('dist-electron', file);
      const stats = fs.statSync(filePath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`  ✅ ${file} (${sizeInMB} MB)`);
    }
  });
}

// 10. 显示 app.asar 大小
const appAsarPath = 'dist-electron/win-unpacked/resources/app.asar';
if (fs.existsSync(appAsarPath)) {
  const stats = fs.statSync(appAsarPath);
  const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
  console.log(`  📦 app.asar: ${sizeInMB} MB`);
}

console.log('\n🎉 优化构建完成！');
console.log('\n💡 优化效果:');
console.log('- ✅ 前端依赖已从打包中排除');
console.log('- ✅ 只打包运行时必需的依赖');
console.log('- ✅ 使用双 package.json 结构');
console.log('- ✅ 减少了 app.asar 体积');
console.log('- ✅ 提高了构建和启动速度');

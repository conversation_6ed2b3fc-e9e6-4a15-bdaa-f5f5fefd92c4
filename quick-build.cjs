const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 快速构建 Windows 安装程序（使用本地 Electron）\n');

// 1. 检查本地 Electron 文件
const electronZip = 'electron-v36.4.0-win32-x64.zip';
if (!fs.existsSync(electronZip)) {
  console.log('❌ 未找到本地 Electron 文件:', electronZip);
  console.log('请确保文件在当前目录中');
  process.exit(1);
}

console.log('✅ 找到本地 Electron 文件');

// 2. 设置缓存目录
const cacheDir = path.resolve('./electron-cache');
if (!fs.existsSync(cacheDir)) {
  fs.mkdirSync(cacheDir, { recursive: true });
  console.log('📁 创建缓存目录:', cacheDir);
}

// 3. 复制 Electron 到缓存
const cachedFile = path.join(cacheDir, electronZip);
if (!fs.existsSync(cachedFile)) {
  console.log('📋 复制 Electron 到缓存...');
  fs.copyFileSync(electronZip, cachedFile);
}

console.log('✅ Electron 缓存准备完成');

// 4. 清理旧的构建
console.log('\n🧹 清理旧构建...');
try {
  if (fs.existsSync('dist-installer')) {
    execSync('rmdir /s /q dist-installer', { stdio: 'inherit', shell: true });
  }
} catch (error) {
  console.log('⚠️ 清理警告:', error.message);
}

// 5. 确保前端已构建
console.log('\n🔨 检查前端构建...');
if (!fs.existsSync('dist')) {
  console.log('构建前端...');
  execSync('pnpm run build', { stdio: 'inherit' });
}
console.log('✅ 前端就绪');

// 6. 设置环境变量并构建
console.log('\n📦 开始构建安装程序...');
console.log('使用缓存:', cacheDir);

try {
  // 设置环境变量
  process.env.ELECTRON_CACHE = cacheDir;
  process.env.ELECTRON_BUILDER_CACHE = cacheDir;
  
  // 构建命令
  const buildCmd = 'pnpm electron-builder --win nsis portable --config.directories.output=dist-installer';
  
  console.log('执行命令:', buildCmd);
  execSync(buildCmd, { 
    stdio: 'inherit',
    env: {
      ...process.env,
      ELECTRON_CACHE: cacheDir,
      ELECTRON_BUILDER_CACHE: cacheDir
    }
  });
  
  console.log('\n✅ 构建完成！');
  
  // 7. 显示结果
  if (fs.existsSync('dist-installer')) {
    console.log('\n📁 生成的文件:');
    const files = fs.readdirSync('dist-installer');
    files.forEach(file => {
      if (file.endsWith('.exe')) {
        const filePath = path.join('dist-installer', file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`   ✅ ${file} (${sizeInMB} MB)`);
      }
    });
    
    console.log('\n🎯 使用说明:');
    console.log('   📦 Setup.exe - 安装版，双击安装到系统');
    console.log('   💾 Portable.exe - 便携版，双击直接运行');
    console.log('\n🚀 安装程序已准备就绪，可以分发给用户！');
  }
  
} catch (error) {
  console.error('\n❌ 构建失败:', error.message);
  console.log('\n💡 可能的解决方案:');
  console.log('1. 确保没有其他进程占用文件');
  console.log('2. 以管理员身份运行');
  console.log('3. 检查磁盘空间是否充足');
  console.log('4. 重启电脑后重试');
  process.exit(1);
}

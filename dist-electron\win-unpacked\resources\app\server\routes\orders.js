const express = require('express');
const router = express.Router();
const { isAdmin, isCashierOrAdmin } = require('../middleware/auth');

// Get database connection - works in both standalone and Electron environments
function getDb() {
  if (global.getDb) {
    // Electron environment
    return global.getDb();
  } else {
    // Standalone server environment
    const { getDb: localGetDb } = require('../db/init');
    return localGetDb();
  }
}
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

// 生成订单编号
function generateOrderNumber() {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = ('0' + (date.getMonth() + 1)).slice(-2);
  const day = ('0' + date.getDate()).slice(-2);
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${year}${month}${day}${random}`;
}

// 获取所有订单 - 收银员和管理员可以访问
router.get('/', isCashierOrAdmin, (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    
    const db = getDb();
    
    // 计算总数
    const totalCount = db.prepare('SELECT COUNT(*) as count FROM orders').get();
    
    // 获取订单列表
    const orders = db.prepare(`
      SELECT o.*, u.username as cashier_name
      FROM orders o
      LEFT JOIN users u ON o.cashier_id = u.id
      ORDER BY o.created_at DESC
      LIMIT ? OFFSET ?
    `).all(parseInt(pageSize), (parseInt(page) - 1) * parseInt(pageSize));
    
    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.order_number,
      totalAmount: order.total_amount,
      paymentMethod: order.payment_method,
      status: order.status,
      remark: order.remark,
      cashierId: order.cashier_id,
      cashierName: order.cashier_name,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      items: [] // 稍后填充
    }));
    
    // 获取每个订单的商品
    for (const order of formattedOrders) {
      order.items = db.prepare(`
        SELECT *
        FROM order_items
        WHERE order_id = ?
      `).all(order.id);
    }
    
    res.json({
      success: true,
      data: formattedOrders,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: totalCount.count
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取订单列表失败'
    });
  }
});

// 获取单个订单详情
router.get('/:id', isCashierOrAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const db = getDb();
    
    // 获取订单基本信息
    const order = db.prepare(`
      SELECT o.*, u.username as cashier_name
      FROM orders o
      LEFT JOIN users u ON o.cashier_id = u.id
      WHERE o.id = ?
    `).get(id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }
    
    // 获取订单商品
    const items = db.prepare(`
      SELECT *
      FROM order_items
      WHERE order_id = ?
    `).all(id);
    
    // 格式化订单数据
    const formattedOrder = {
      id: order.id,
      orderNumber: order.order_number,
      totalAmount: order.total_amount,
      paymentMethod: order.payment_method,
      status: order.status,
      remark: order.remark,
      cashierId: order.cashier_id,
      cashierName: order.cashier_name,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      items: items
    };
    
    res.json({
      success: true,
      data: formattedOrder
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取订单详情失败'
    });
  }
});

// 创建订单 - 收银员和管理员可以创建
router.post('/', isCashierOrAdmin, (req, res) => {
  try {
    let { totalAmount, paymentMethod, remark, items, status = 'pending' } = req.body;
    
    // 验证必填字段
    if (!paymentMethod || !items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '订单信息不完整'
      });
    }
    
    const db = getDb();
    
    // 检查所有商品是否可用
    const invalidProducts = [];
    for (const item of items) {
      const product = db.prepare(`
        SELECT id, name, is_available FROM products WHERE id = ?
      `).get(item.productId);
      
      if (!product) {
        invalidProducts.push({
          id: item.productId,
          name: item.productName,
          reason: '商品不存在'
        });
        continue;
      }
      
      if (product.is_available !== 1) {
        invalidProducts.push({
          id: product.id,
          name: product.name,
          reason: '商品已下架'
        });
      }
    }
    
    // 如果存在不可用商品，返回错误
    if (invalidProducts.length > 0) {
      return res.status(400).json({
        success: false,
        message: '订单中包含不可用商品',
        invalidProducts
      });
    }
    
    // 重新计算订单总金额，确保正确
    const calculatedTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    totalAmount = calculatedTotal; // 使用计算的金额，忽略前端传入的值
    
    // 开始事务
    db.prepare('BEGIN TRANSACTION').run();
    
    try {
      // 生成订单编号
      const orderNumber = generateOrderNumber();
      
      // 创建订单
      const result = db.prepare(`
        INSERT INTO orders
        (order_number, total_amount, payment_method, status, remark, cashier_id)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        orderNumber,
        totalAmount,
        paymentMethod,
        status, // 允许设置初始状态
        remark || null,
        req.user.id // 从认证信息获取
      );
      
      const orderId = result.lastInsertRowid;
      
      // 添加订单商品
      const insertOrderItem = db.prepare(`
        INSERT INTO order_items
        (order_id, product_id, product_name, option_id, option_name, price, quantity, remark)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      for (const item of items) {
        insertOrderItem.run(
          orderId,
          item.productId,
          item.productName,
          item.optionId,
          item.optionName,
          item.price,
          item.quantity,
          item.remark || null
        );
      }
      
      // 提交事务
      db.prepare('COMMIT').run();
      
      // 获取创建的订单
      const newOrder = db.prepare(`
        SELECT o.*, u.username as cashier_name
        FROM orders o
        LEFT JOIN users u ON o.cashier_id = u.id
        WHERE o.id = ?
      `).get(orderId);
      
      // 获取订单商品
      const orderItems = db.prepare(`
        SELECT *
        FROM order_items
        WHERE order_id = ?
      `).all(orderId);
      
      // 格式化订单数据
      const formattedOrder = {
        id: newOrder.id,
        orderNumber: newOrder.order_number,
        totalAmount: newOrder.total_amount,
        paymentMethod: newOrder.payment_method,
        status: newOrder.status,
        remark: newOrder.remark,
        cashierId: newOrder.cashier_id,
        cashierName: newOrder.cashier_name,
        createdAt: newOrder.created_at,
        updatedAt: newOrder.updated_at,
        items: orderItems
      };
      
      res.status(201).json({
        success: true,
        data: formattedOrder
      });
    } catch (error) {
      // 回滚事务
      db.prepare('ROLLBACK').run();
      throw error;
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建订单失败'
    });
  }
});

// 更新订单状态
router.put('/:id/status', isCashierOrAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!status || !['pending', 'completed', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的订单状态'
      });
    }
    
    const db = getDb();
    
    // 检查订单是否存在
    const existingOrder = db.prepare('SELECT id FROM orders WHERE id = ?').get(id);
    
    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }
    
    // 更新订单状态
    db.prepare(`
      UPDATE orders
      SET status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(status, id);
    
    // 获取更新后的订单
    const updatedOrder = db.prepare(`
      SELECT o.*, u.username as cashier_name
      FROM orders o
      LEFT JOIN users u ON o.cashier_id = u.id
      WHERE o.id = ?
    `).get(id);
    
    // 格式化订单数据
    const formattedOrder = {
      id: updatedOrder.id,
      orderNumber: updatedOrder.order_number,
      totalAmount: updatedOrder.total_amount,
      paymentMethod: updatedOrder.payment_method,
      status: updatedOrder.status,
      remark: updatedOrder.remark,
      cashierId: updatedOrder.cashier_id,
      cashierName: updatedOrder.cashier_name,
      createdAt: updatedOrder.created_at,
      updatedAt: updatedOrder.updated_at
    };
    
    res.json({
      success: true,
      data: formattedOrder
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新订单状态失败'
    });
  }
});

// 删除订单 - 仅限管理员
router.delete('/:id', isAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const db = getDb();
    
    // 检查订单是否存在
    const existingOrder = db.prepare('SELECT id FROM orders WHERE id = ?').get(id);
    
    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }
    
    // 使用事务确保数据一致性
    db.prepare('BEGIN TRANSACTION').run();
    
    try {
      // 先删除订单关联的商品项
      db.prepare('DELETE FROM order_items WHERE order_id = ?').run(id);
      
      // 然后删除订单
      db.prepare('DELETE FROM orders WHERE id = ?').run(id);
      
      // 提交事务
      db.prepare('COMMIT').run();
      
      res.json({
        success: true,
        message: '订单已成功删除'
      });
    } catch (error) {
      // 出错时回滚事务
      db.prepare('ROLLBACK').run();
      throw error;
    }
  } catch (error) {
    console.error('删除订单失败:', error);
    res.status(500).json({
      success: false,
      message: '删除订单失败'
    });
  }
});

// 导出订单数据
router.get('/export/:startDate/:endDate', isCashierOrAdmin, (req, res) => {
  try {
    const { startDate, endDate } = req.params;
    const db = getDb();
    
    // 获取指定日期范围内的订单
    const orders = db.prepare(`
      SELECT o.*, u.username as cashier_name
      FROM orders o
      LEFT JOIN users u ON o.cashier_id = u.id
      WHERE DATE(o.created_at) BETWEEN DATE(?) AND DATE(?)
      ORDER BY o.created_at DESC
    `).all(startDate, endDate);
    
    // 设置导出文件头
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=orders_${startDate}_to_${endDate}.csv`);
    
    // CSV头行
    res.write('订单号,日期,金额,支付方式,状态,收银员,备注\n');
    
    // 写入订单数据
    orders.forEach(order => {
      const row = [
        order.order_number,
        order.created_at,
        order.total_amount,
        order.payment_method,
        order.status,
        order.cashier_name,
        order.remark || ''
      ];
      
      // 处理CSV中的特殊字符
      const escapedRow = row.map(val => {
        if (typeof val === 'string' && (val.includes(',') || val.includes('"') || val.includes('\n'))) {
          return `"${val.replace(/"/g, '""')}"`;
        }
        return val;
      });
      
      res.write(escapedRow.join(',') + '\n');
    });
    
    res.end();
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '导出订单数据失败'
    });
  }
});

module.exports = router; 
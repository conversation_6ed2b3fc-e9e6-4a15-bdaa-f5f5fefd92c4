const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Global database connection
let db = null;

/**
 * Initialize database for Electron environment
 * @param {string} dbPath - Custom database path for Electron
 */
function initDatabase(dbPath) {
  return new Promise((resolve, reject) => {
    try {
      // Check if database file exists
      const dbExists = fs.existsSync(dbPath);
      
      // Ensure database directory exists
      const dbDir = path.dirname(dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }
      
      // Create database connection
      db = new Database(dbPath);
      
      // Read database schema
      let schemaPath = path.join(dbDir, 'schema.sql');
      if (!fs.existsSync(schemaPath)) {
        // Fallback to bundled schema
        schemaPath = path.join(__dirname, '../server/db/schema.sql');
      }
      
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      // Execute SQL script
      db.exec(schema);
      
      // Initialize default users
      initDefaultUsers();
      
      // If new database, import initial data
      if (!dbExists) {
        importInitialData(dbDir);
      }
      
      console.log('Database initialized successfully');
      resolve(db);
    } catch (err) {
      reject(err);
    }
  });
}

/**
 * Initialize default users
 */
function initDefaultUsers() {
  const usersExist = db.prepare('SELECT COUNT(*) as count FROM users').get();
  
  if (usersExist.count === 0) {
    // Create default admin user
    db.prepare(
      'INSERT INTO users (username, password, role) VALUES (?, ?, ?)'
    ).run('admin', 'admin123', 'admin');
    
    // Create default cashier user
    db.prepare(
      'INSERT INTO users (username, password, role) VALUES (?, ?, ?)'
    ).run('cashier', 'cashier123', 'cashier');
    
    console.log('Default users created');
  }
}

/**
 * Import initial sample data
 * @param {string} dbDir - Database directory path
 */
function importInitialData(dbDir) {
  try {
    let initialDataPath = path.join(dbDir, 'initial-data.sql');
    if (!fs.existsSync(initialDataPath)) {
      // Fallback to bundled initial data
      initialDataPath = path.join(__dirname, '../server/db/initial-data.sql');
    }
    
    if (fs.existsSync(initialDataPath)) {
      const initialData = fs.readFileSync(initialDataPath, 'utf8');
      
      // Execute initial data script
      db.exec(initialData);
      
      console.log('Initial data imported successfully');
    }
  } catch (err) {
    console.error('Failed to import initial data:', err);
  }
}

/**
 * Get database connection
 */
function getDb() {
  if (!db) {
    throw new Error('Database not initialized');
  }
  return db;
}

module.exports = {
  initDatabase,
  getDb,
};

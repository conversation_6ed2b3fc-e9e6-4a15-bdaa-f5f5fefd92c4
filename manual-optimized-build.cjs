const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 手动优化构建 (使用现有构建)...\n');

try {
  // 1. 检查现有构建
  console.log('🔍 检查现有构建...');
  if (!fs.existsSync('dist')) {
    console.log('❌ 未找到 dist 目录，请先运行: pnpm run build');
    process.exit(1);
  }
  console.log('✅ 找到现有前端构建\n');

  // 2. 清理旧的 app 目录
  console.log('🧹 清理旧的 app 目录...');
  if (fs.existsSync('app')) {
    fs.rmSync('app', { recursive: true, force: true });
  }
  console.log('✅ 清理完成\n');

  // 3. 创建 app 目录结构
  console.log('📁 创建 app 目录结构...');
  const dirs = [
    'app',
    'app/dist',
    'app/electron',
    'app/server',
    'app/server/db',
    'app/server/routes',
    'app/server/middleware'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`  ✓ 创建 ${dir}`);
    }
  });

  // 4. 复制文件
  console.log('\n📦 复制文件...');
  
  // 复制前端构建文件
  console.log('  📋 复制前端文件...');
  execSync('xcopy dist app\\dist /E /I /H /Y /Q', { stdio: 'pipe' });
  console.log('  ✓ 前端文件复制完成');
  
  // 复制 Electron 文件
  console.log('  ⚡ 复制 Electron 文件...');
  const electronFiles = [
    'electron/main.js',
    'electron/server.js',
    'electron/db-init.js'
  ];
  
  electronFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join('app', file);
      fs.copyFileSync(file, destFile);
      console.log(`    ✓ ${file}`);
    }
  });
  
  // 复制服务器文件
  console.log('  🖥️ 复制服务器文件...');
  const serverFiles = [
    'server/index.js',
    'server/db/init.js',
    'server/db/schema.sql',
    'server/db/initial-data.sql',
    'server/routes/auth.js',
    'server/routes/products.js',
    'server/routes/categories.js',
    'server/routes/orders.js',
    'server/routes/reports.js',
    'server/middleware/auth.js',
    'server/middleware/logger.js'
  ];
  
  serverFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join('app', file);
      fs.copyFileSync(file, destFile);
      console.log(`    ✓ ${file}`);
    }
  });

  // 5. 在 app 目录安装生产依赖
  console.log('\n📦 安装生产依赖...');
  const originalDir = process.cwd();
  process.chdir('app');
  
  try {
    execSync('pnpm install --prod --silent', { stdio: 'inherit' });
    console.log('✅ 生产依赖安装完成');
  } catch (error) {
    console.log('⚠️ pnpm 安装失败，尝试使用 npm...');
    execSync('npm install --production --silent', { stdio: 'inherit' });
    console.log('✅ 生产依赖安装完成 (npm)');
  }
  
  process.chdir(originalDir);

  // 5.5. 确保 app/package.json 存在
  console.log('\n📄 检查 app/package.json...');
  const appPackageJson = 'app/package.json';
  if (!fs.existsSync(appPackageJson)) {
    console.log('❌ app/package.json 不存在，请确保已创建');
    process.exit(1);
  }
  console.log('✅ app/package.json 存在');

  // 6. 清理 app/node_modules 中的不必要文件
  console.log('\n🧹 清理不必要文件...');
  const appNodeModules = 'app/node_modules';
  if (fs.existsSync(appNodeModules)) {
    // 删除测试文件和文档
    const cleanupPatterns = [
      '**/*.md',
      '**/test/**',
      '**/tests/**',
      '**/__tests__/**',
      '**/docs/**',
      '**/examples/**',
      '**/*.map',
      '**/.eslintrc*',
      '**/tsconfig.json'
    ];
    
    let cleanedFiles = 0;
    function cleanDir(dir) {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          cleanDir(itemPath);
        } else {
          // 检查是否需要删除
          if (item.endsWith('.md') || item.endsWith('.map') || 
              item.includes('test') || item.includes('spec') ||
              item.startsWith('.eslint') || item === 'tsconfig.json') {
            try {
              fs.unlinkSync(itemPath);
              cleanedFiles++;
            } catch (err) {
              // 忽略错误
            }
          }
        }
      }
    }
    
    cleanDir(appNodeModules);
    console.log(`  ✓ 清理了 ${cleanedFiles} 个不必要文件`);
  }

  // 7. 运行 electron-builder
  console.log('\n🔧 运行 electron-builder...');
  execSync('npx electron-builder --config.directories.app=app', { stdio: 'inherit' });
  console.log('✅ 构建完成\n');

  // 8. 显示结果
  console.log('📊 构建结果:');
  if (fs.existsSync('dist-electron')) {
    const files = fs.readdirSync('dist-electron');
    files.forEach(file => {
      if (file.endsWith('.exe')) {
        const filePath = path.join('dist-electron', file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`  ✅ ${file} (${sizeInMB} MB)`);
      }
    });
  }

  // 9. 显示 app.asar 大小
  const appAsarPath = 'dist-electron/win-unpacked/resources/app.asar';
  if (fs.existsSync(appAsarPath)) {
    const stats = fs.statSync(appAsarPath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`  📦 app.asar: ${sizeInMB} MB (优化后)`);
  }

  console.log('\n🎉 优化构建完成！');
  console.log('\n💡 优化效果:');
  console.log('- ✅ 使用双 package.json 结构');
  console.log('- ✅ 只打包运行时必需的依赖');
  console.log('- ✅ 清理了不必要的文件');
  console.log('- ✅ 显著减少了应用体积');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}

const fs = require('fs');
const path = require('path');

console.log('🔍 分析现有构建版本...\n');

// 1. 分析便携版
const portablePath = 'distribution/Shop-Desktop-Portable';
if (fs.existsSync(portablePath)) {
  console.log('📦 便携版分析:');
  
  // 计算总大小
  let totalSize = 0;
  let fileCount = 0;
  
  function calculateDirSize(dir) {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const itemPath = path.join(dir, item);
      try {
        const stat = fs.statSync(itemPath);
        if (stat.isDirectory()) {
          calculateDirSize(itemPath);
        } else {
          totalSize += stat.size;
          fileCount++;
        }
      } catch (err) {
        // 忽略权限错误
      }
    }
  }
  
  calculateDirSize(portablePath);
  const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
  console.log(`  📊 总大小: ${sizeInMB} MB (${fileCount} 个文件)`);
  
  // 分析 app 目录
  const appPath = path.join(portablePath, 'resources/app');
  if (fs.existsSync(appPath)) {
    console.log('\n📁 app 目录分析:');
    
    let appSize = 0;
    let appFileCount = 0;
    
    function calculateAppSize(dir) {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const itemPath = path.join(dir, item);
        try {
          const stat = fs.statSync(itemPath);
          if (stat.isDirectory()) {
            calculateAppSize(itemPath);
          } else {
            appSize += stat.size;
            appFileCount++;
          }
        } catch (err) {
          // 忽略权限错误
        }
      }
    }
    
    calculateAppSize(appPath);
    const appSizeInMB = (appSize / (1024 * 1024)).toFixed(2);
    console.log(`  📦 app 目录大小: ${appSizeInMB} MB (${appFileCount} 个文件)`);
    
    // 检查 package.json
    const packageJsonPath = path.join(appPath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const deps = Object.keys(packageJson.dependencies || {});
      const devDeps = Object.keys(packageJson.devDependencies || {});
      
      console.log(`  📚 dependencies: ${deps.length} 个`);
      console.log(`  🛠️ devDependencies: ${devDeps.length} 个`);
      
      console.log('\n📋 dependencies 列表:');
      deps.forEach(dep => console.log(`    - ${dep}`));
      
      if (deps.length > 10) {
        console.log('\n⚠️ 发现问题: dependencies 中包含过多依赖！');
        console.log('🎯 优化建议: 应该只保留运行时必需的依赖');
      }
    }
    
    // 检查 node_modules
    const nodeModulesPath = path.join(appPath, 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
      const nodeModulesItems = fs.readdirSync(nodeModulesPath);
      console.log(`\n📚 node_modules: ${nodeModulesItems.length} 个包`);
      
      if (nodeModulesItems.length > 0) {
        let nodeModulesSize = 0;
        let nodeModulesFileCount = 0;
        
        function calculateNodeModulesSize(dir) {
          const items = fs.readdirSync(dir);
          for (const item of items) {
            const itemPath = path.join(dir, item);
            try {
              const stat = fs.statSync(itemPath);
              if (stat.isDirectory()) {
                calculateNodeModulesSize(itemPath);
              } else {
                nodeModulesSize += stat.size;
                nodeModulesFileCount++;
              }
            } catch (err) {
              // 忽略权限错误
            }
          }
        }
        
        calculateNodeModulesSize(nodeModulesPath);
        const nodeModulesSizeInMB = (nodeModulesSize / (1024 * 1024)).toFixed(2);
        console.log(`  📦 node_modules 大小: ${nodeModulesSizeInMB} MB (${nodeModulesFileCount} 个文件)`);
        
        // 列出主要包
        console.log('\n📦 主要包:');
        nodeModulesItems.slice(0, 10).forEach(item => {
          if (!item.startsWith('.')) {
            console.log(`    - ${item}`);
          }
        });
        
        if (nodeModulesItems.length > 10) {
          console.log(`    ... 还有 ${nodeModulesItems.length - 10} 个包`);
        }
      } else {
        console.log('  📦 node_modules 为空');
      }
    }
    
    // 检查 dist 目录
    const distPath = path.join(appPath, 'dist');
    if (fs.existsSync(distPath)) {
      let distSize = 0;
      let distFileCount = 0;
      
      function calculateDistSize(dir) {
        const items = fs.readdirSync(dir);
        for (const item of items) {
          const itemPath = path.join(dir, item);
          try {
            const stat = fs.statSync(itemPath);
            if (stat.isDirectory()) {
              calculateDistSize(itemPath);
            } else {
              distSize += stat.size;
              distFileCount++;
            }
          } catch (err) {
            // 忽略权限错误
          }
        }
      }
      
      calculateDistSize(distPath);
      const distSizeInMB = (distSize / (1024 * 1024)).toFixed(2);
      console.log(`\n🎨 前端构建 (dist): ${distSizeInMB} MB (${distFileCount} 个文件)`);
    }
  }
  
  // 分析 Electron 核心文件
  console.log('\n⚡ Electron 核心文件:');
  const electronFiles = [
    'Shop Desktop.exe',
    'resources/electron.asar',
    'resources/default_app.asar'
  ];
  
  electronFiles.forEach(file => {
    const filePath = path.join(portablePath, file);
    if (fs.existsSync(filePath)) {
      const stat = fs.statSync(filePath);
      const sizeInMB = (stat.size / (1024 * 1024)).toFixed(2);
      console.log(`  📄 ${file}: ${sizeInMB} MB`);
    }
  });
  
} else {
  console.log('❌ 未找到便携版目录');
}

// 2. 优化建议
console.log('\n💡 优化分析:');

// 检查根目录的 package.json
if (fs.existsSync('package.json')) {
  const rootPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const rootDeps = Object.keys(rootPackageJson.dependencies || {});
  const rootDevDeps = Object.keys(rootPackageJson.devDependencies || {});
  
  console.log('\n📋 根目录 package.json:');
  console.log(`  📚 dependencies: ${rootDeps.length} 个`);
  console.log(`  🛠️ devDependencies: ${rootDevDeps.length} 个`);
  
  if (rootDeps.length <= 10) {
    console.log('  ✅ dependencies 数量合理');
  } else {
    console.log('  ⚠️ dependencies 过多，建议移至 devDependencies');
  }
  
  // 分析运行时必需依赖
  const runtimeDeps = [
    'better-sqlite3',
    'body-parser',
    'cors', 
    'express',
    'jsonwebtoken'
  ];
  
  console.log('\n🎯 运行时必需依赖:');
  runtimeDeps.forEach(dep => {
    if (rootDeps.includes(dep)) {
      console.log(`  ✅ ${dep} - 正确位置`);
    } else {
      console.log(`  ❌ ${dep} - 缺失或位置错误`);
    }
  });
  
  // 分析前端依赖
  const frontendDeps = [
    'react',
    'react-dom',
    '@radix-ui/react-alert-dialog',
    'tailwindcss',
    'vite'
  ];
  
  console.log('\n🎨 前端依赖检查:');
  frontendDeps.forEach(dep => {
    if (rootDevDeps.includes(dep)) {
      console.log(`  ✅ ${dep} - 正确位置 (devDependencies)`);
    } else if (rootDeps.includes(dep)) {
      console.log(`  ⚠️ ${dep} - 应移至 devDependencies`);
    } else {
      console.log(`  ❓ ${dep} - 未找到`);
    }
  });
}

console.log('\n🚀 优化建议总结:');
console.log('1. ✅ 依赖结构已基本优化');
console.log('2. 📦 便携版可直接使用');
console.log('3. 🎯 如需进一步优化，可重新构建');
console.log('4. 📊 当前版本已经相对精简');

console.log('\n📋 使用建议:');
console.log('- 🚀 当前便携版可直接分发');
console.log('- 📦 功能完整，体积合理');
console.log('- 🔧 如需更小体积，可手动优化 node_modules');
console.log('- 📈 性能表现应该良好');

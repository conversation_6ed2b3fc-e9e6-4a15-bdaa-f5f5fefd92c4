const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 强制构建 Windows 安装程序\n');

// 1. 强制终止可能占用文件的进程
console.log('🔄 终止相关进程...');
try {
  execSync('taskkill /f /im "Shop Desktop.exe" 2>nul', { stdio: 'ignore' });
  execSync('taskkill /f /im "electron.exe" 2>nul', { stdio: 'ignore' });
  execSync('taskkill /f /im "app-builder.exe" 2>nul', { stdio: 'ignore' });
  console.log('✅ 进程清理完成');
} catch (error) {
  console.log('⚠️ 进程清理警告 (可忽略)');
}

// 2. 等待文件释放
console.log('⏳ 等待文件释放...');
setTimeout(() => {
  
  // 3. 强制删除构建目录
  console.log('🧹 强制清理构建目录...');
  try {
    if (fs.existsSync('dist-installer')) {
      // 使用 PowerShell 强制删除
      execSync('powershell -Command "Remove-Item -Recurse -Force dist-installer -ErrorAction SilentlyContinue"', { stdio: 'inherit' });
    }
    if (fs.existsSync('dist-electron')) {
      execSync('powershell -Command "Remove-Item -Recurse -Force dist-electron -ErrorAction SilentlyContinue"', { stdio: 'inherit' });
    }
    console.log('✅ 目录清理完成');
  } catch (error) {
    console.log('⚠️ 清理警告:', error.message);
  }

  // 4. 检查和准备 Electron 缓存
  const electronZip = 'electron-v36.4.0-win32-x64.zip';
  if (!fs.existsSync(electronZip)) {
    console.log('❌ 未找到本地 Electron 文件:', electronZip);
    process.exit(1);
  }

  const cacheDir = path.resolve('./electron-cache');
  if (!fs.existsSync(cacheDir)) {
    fs.mkdirSync(cacheDir, { recursive: true });
  }

  const cachedFile = path.join(cacheDir, electronZip);
  if (!fs.existsSync(cachedFile)) {
    fs.copyFileSync(electronZip, cachedFile);
  }
  console.log('✅ Electron 缓存就绪');

  // 5. 确保前端已构建
  if (!fs.existsSync('dist')) {
    console.log('🔨 构建前端...');
    execSync('pnpm run build', { stdio: 'inherit' });
  }
  console.log('✅ 前端就绪');

  // 6. 使用简化配置构建
  console.log('\n📦 开始构建 (简化配置)...');
  
  try {
    // 设置环境变量
    const env = {
      ...process.env,
      ELECTRON_CACHE: cacheDir,
      ELECTRON_BUILDER_CACHE: cacheDir
    };
    
    // 只构建 NSIS 安装程序，避免复杂配置
    const buildCmd = 'pnpm electron-builder --win nsis --config.directories.output=dist-installer --config.compression=normal';
    
    console.log('执行:', buildCmd);
    execSync(buildCmd, { stdio: 'inherit', env });
    
    console.log('\n✅ 构建完成！');
    
    // 显示结果
    if (fs.existsSync('dist-installer')) {
      console.log('\n📁 生成的文件:');
      const files = fs.readdirSync('dist-installer');
      files.forEach(file => {
        if (file.endsWith('.exe')) {
          const filePath = path.join('dist-installer', file);
          const stats = fs.statSync(filePath);
          const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
          console.log(`   ✅ ${file} (${sizeInMB} MB)`);
        }
      });
      
      console.log('\n🎯 安装程序说明:');
      console.log('   📦 Setup.exe - Windows 安装程序');
      console.log('   🚀 双击安装到系统，创建快捷方式');
      console.log('   💾 支持卸载和更新');
      
      console.log('\n📋 分发步骤:');
      console.log('1. 将 Setup.exe 复制到目标设备');
      console.log('2. 双击运行安装程序');
      console.log('3. 按照向导完成安装');
      console.log('4. 从开始菜单或桌面启动应用');
    }
    
  } catch (error) {
    console.error('\n❌ 构建失败:', error.message);
    
    console.log('\n🔧 手动解决方案:');
    console.log('1. 重启电脑清理所有进程');
    console.log('2. 或者使用现有的 dist-electron/win-unpacked 目录');
    console.log('3. 将整个 win-unpacked 文件夹打包为 ZIP');
    console.log('4. 用户解压后直接运行 Shop Desktop.exe');
    
    process.exit(1);
  }

}, 3000); // 等待 3 秒让进程完全退出

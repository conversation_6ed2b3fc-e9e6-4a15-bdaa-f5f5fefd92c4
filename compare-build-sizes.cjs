const fs = require('fs');
const path = require('path');

console.log('📊 构建大小对比分析\n');

// 1. 分析当前构建
console.log('🔍 分析当前构建...');

// 检查 app.asar 大小
const currentAppAsar = 'dist-electron/win-unpacked/resources/app.asar';
if (fs.existsSync(currentAppAsar)) {
  const stats = fs.statSync(currentAppAsar);
  const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
  console.log(`📦 当前 app.asar 大小: ${sizeInMB} MB`);
} else {
  console.log('❌ 未找到当前构建的 app.asar');
}

// 2. 分析依赖结构
console.log('\n📋 依赖分析:');

// 分析根目录 package.json
const rootPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const prodDeps = Object.keys(rootPackage.dependencies || {});
const devDeps = Object.keys(rootPackage.devDependencies || {});

console.log(`🔧 生产依赖 (会被打包): ${prodDeps.length} 个`);
prodDeps.forEach(dep => console.log(`  - ${dep}`));

console.log(`\n🛠️ 开发依赖 (不会被打包): ${devDeps.length} 个`);
console.log('  (包括所有前端框架和构建工具)');

// 3. 分析 node_modules 大小
console.log('\n📁 node_modules 分析:');
if (fs.existsSync('node_modules')) {
  let totalSize = 0;
  let fileCount = 0;
  
  function calculateDirSize(dir) {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const itemPath = path.join(dir, item);
      try {
        const stat = fs.statSync(itemPath);
        if (stat.isDirectory()) {
          calculateDirSize(itemPath);
        } else {
          totalSize += stat.size;
          fileCount++;
        }
      } catch (err) {
        // 忽略权限错误
      }
    }
  }
  
  calculateDirSize('node_modules');
  const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
  console.log(`📦 node_modules 总大小: ${sizeInMB} MB (${fileCount} 个文件)`);
}

// 4. 优化建议
console.log('\n💡 优化建议:');
console.log('');
console.log('🎯 当前问题:');
console.log('- ❌ 所有依赖都在 dependencies 中，会被打包');
console.log('- ❌ 前端框架 (React, Vite 等) 被打包到 app.asar');
console.log('- ❌ 开发工具 (TypeScript, ESLint 等) 被打包');
console.log('- ❌ 没有使用双 package.json 结构');

console.log('\n✅ 优化方案:');
console.log('1. 移动前端依赖到 devDependencies');
console.log('2. 只保留运行时必需的依赖在 dependencies');
console.log('3. 使用双 package.json 结构');
console.log('4. 清理不必要的文件');

console.log('\n📈 预期优化效果:');
console.log('- 📦 app.asar 体积减少 60-80%');
console.log('- ⚡ 应用启动速度提升');
console.log('- 💾 安装包大小减少');
console.log('- 🔒 源码保护更好');

// 5. 显示优化后的依赖结构
console.log('\n🎯 优化后的依赖结构:');
console.log('');
console.log('📦 生产依赖 (app/package.json):');
const optimizedDeps = [
  'better-sqlite3',
  'body-parser', 
  'cors',
  'express',
  'jsonwebtoken'
];
optimizedDeps.forEach(dep => console.log(`  ✅ ${dep}`));

console.log('\n🛠️ 开发依赖 (根目录 package.json):');
const frontendDeps = [
  'react',
  'react-dom',
  'vite',
  'typescript',
  '@radix-ui/*',
  'tailwindcss',
  'electron',
  'electron-builder'
];
frontendDeps.forEach(dep => console.log(`  🔧 ${dep}`));

// 6. 实施步骤
console.log('\n🚀 实施步骤:');
console.log('');
console.log('1️⃣ 重新组织依赖:');
console.log('   pnpm remove <前端依赖> --save');
console.log('   pnpm add <前端依赖> --save-dev');
console.log('');
console.log('2️⃣ 创建 app/package.json:');
console.log('   只包含运行时必需的依赖');
console.log('');
console.log('3️⃣ 修改 electron-builder 配置:');
console.log('   设置 directories.app = "app"');
console.log('');
console.log('4️⃣ 运行优化构建:');
console.log('   node final-optimized-build.cjs');

console.log('\n📋 注意事项:');
console.log('- ⚠️ 确保没有 Electron 进程在运行');
console.log('- ⚠️ 备份当前的 package.json');
console.log('- ⚠️ 测试优化后的应用功能');
console.log('- ⚠️ 检查所有依赖都正确分类');

console.log('\n🎉 优化完成后，你的应用将更小、更快、更专业！');

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始简化优化构建...\n');

try {
  // 1. 构建前端
  console.log('🔨 构建前端...');
  execSync('pnpm run build', { stdio: 'inherit' });
  console.log('✅ 前端构建完成\n');

  // 2. 创建 app 目录
  console.log('📁 创建 app 目录...');
  if (!fs.existsSync('app')) {
    fs.mkdirSync('app');
  }
  
  // 创建子目录
  const dirs = ['app/dist', 'app/electron', 'app/server', 'app/server/db', 'app/server/routes', 'app/server/middleware'];
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`  ✓ 创建 ${dir}`);
    }
  });

  // 3. 复制文件
  console.log('\n📦 复制文件...');
  
  // 复制前端构建文件
  if (fs.existsSync('dist')) {
    execSync('xcopy dist app\\dist /E /I /H /Y', { stdio: 'inherit' });
    console.log('  ✓ 复制前端文件');
  }
  
  // 复制 Electron 文件
  const electronFiles = ['electron/main.js', 'electron/server.js', 'electron/db-init.js'];
  electronFiles.forEach(file => {
    if (fs.existsSync(file)) {
      fs.copyFileSync(file, `app/${file}`);
      console.log(`  ✓ 复制 ${file}`);
    }
  });
  
  // 复制服务器文件
  const serverFiles = [
    'server/index.js',
    'server/db/init.js',
    'server/db/schema.sql',
    'server/db/initial-data.sql',
    'server/routes/auth.js',
    'server/routes/products.js',
    'server/routes/categories.js',
    'server/routes/orders.js',
    'server/routes/reports.js',
    'server/middleware/auth.js',
    'server/middleware/logger.js'
  ];
  
  serverFiles.forEach(file => {
    if (fs.existsSync(file)) {
      fs.copyFileSync(file, `app/${file}`);
      console.log(`  ✓ 复制 ${file}`);
    }
  });

  // 4. 在 app 目录安装依赖
  console.log('\n📦 安装生产依赖...');
  process.chdir('app');
  execSync('pnpm install --prod', { stdio: 'inherit' });
  process.chdir('..');
  console.log('✅ 依赖安装完成\n');

  // 5. 运行 electron-builder
  console.log('🔧 运行 electron-builder...');
  execSync('pnpm run build-electron', { stdio: 'inherit' });
  console.log('✅ 构建完成\n');

  // 6. 显示结果
  console.log('📊 构建结果:');
  if (fs.existsSync('dist-electron')) {
    const files = fs.readdirSync('dist-electron');
    files.forEach(file => {
      if (file.endsWith('.exe')) {
        const filePath = path.join('dist-electron', file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`  ✅ ${file} (${sizeInMB} MB)`);
      }
    });
  }

  console.log('\n🎉 优化构建完成！');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}

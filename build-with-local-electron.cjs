const { build } = require('electron-builder');
const path = require('path');
const fs = require('fs');

async function buildWithLocalElectron() {
  try {
    console.log('🚀 使用本地 Electron 构建安装程序...\n');
    
    // 检查本地 Electron 文件
    const electronZip = 'electron-v36.4.0-win32-x64.zip';
    if (!fs.existsSync(electronZip)) {
      console.log('❌ 未找到本地 Electron 文件:', electronZip);
      console.log('请确保文件在当前目录中');
      return;
    }
    
    console.log('✅ 找到本地 Electron 文件:', electronZip);
    
    // 确保缓存目录存在
    const cacheDir = './electron-cache';
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }
    
    // 复制到缓存目录
    const cachedFile = path.join(cacheDir, electronZip);
    if (!fs.existsSync(cachedFile)) {
      console.log('📋 复制 Electron 到缓存目录...');
      fs.copyFileSync(electronZip, cachedFile);
    }
    
    console.log('✅ Electron 缓存准备完成');
    
    // 确保前端已构建
    console.log('\n1. 检查前端构建...');
    const distPath = path.join(__dirname, 'dist');
    if (!fs.existsSync(distPath)) {
      console.log('   前端未构建，正在构建...');
      const { execSync } = require('child_process');
      execSync('pnpm run build', { stdio: 'inherit' });
    }
    console.log('   ✅ 前端构建完成');

    // 设置环境变量使用本地缓存
    process.env.ELECTRON_CACHE = path.resolve(cacheDir);
    process.env.ELECTRON_BUILDER_CACHE = path.resolve(cacheDir);
    
    console.log('\n2. 开始构建安装程序...');
    console.log('   使用缓存目录:', process.env.ELECTRON_CACHE);
    
    // 构建配置 - 简化版，避免复杂配置
    const config = {
      appId: 'com.shop.desktop',
      productName: 'Shop Desktop',
      directories: {
        output: 'dist-installer'
      },
      electronDownload: {
        cache: cacheDir
      },
      asar: true,
      files: [
        'dist/**/*',
        'electron/**/*',
        'server/**/*',
        '!server/node_modules',
        '!**/*.map',
        '!**/test/**/*',
        '!**/tests/**/*',
        '!**/*.md'
      ],
      extraResources: [
        {
          from: 'server/db/schema.sql',
          to: 'server/db/schema.sql'
        },
        {
          from: 'server/db/initial-data.sql',
          to: 'server/db/initial-data.sql'
        }
      ],
      win: {
        target: [
          {
            target: 'nsis',
            arch: ['x64']
          },
          {
            target: 'portable',
            arch: ['x64']
          }
        ],
        icon: 'public/vite.svg'
      },
      nsis: {
        oneClick: false,
        allowToChangeInstallationDirectory: true,
        createDesktopShortcut: true,
        createStartMenuShortcut: true,
        artifactName: '${productName}-${version}-Setup.${ext}'
      },
      portable: {
        artifactName: '${productName}-${version}-Portable.${ext}'
      },
      nodeGypRebuild: false,
      buildDependenciesFromSource: false,
      compression: 'normal', // 使用普通压缩以加快速度
      npmRebuild: false
    };

    // 执行构建
    const result = await build({
      config,
      win: ['nsis', 'portable'],
      publish: 'never'
    });

    console.log('\n✅ 构建完成！');
    
    // 显示构建结果
    const outputDir = path.resolve('dist-installer');
    console.log(`\n📦 安装程序位置: ${outputDir}`);
    
    if (fs.existsSync(outputDir)) {
      const files = fs.readdirSync(outputDir);
      console.log('\n📁 生成的文件:');
      files.forEach(file => {
        if (file.endsWith('.exe')) {
          const filePath = path.join(outputDir, file);
          const stats = fs.statSync(filePath);
          const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
          console.log(`   ✅ ${file} (${sizeInMB} MB)`);
        }
      });
    }
    
    console.log('\n🎯 使用说明:');
    console.log('   📦 Setup.exe - 双击安装到系统，创建快捷方式');
    console.log('   💾 Portable.exe - 双击直接运行，无需安装');
    
    return result;
  } catch (error) {
    console.error('❌ 构建失败:', error);
    throw error;
  }
}

// 运行构建
if (require.main === module) {
  buildWithLocalElectron()
    .then(() => {
      console.log('\n🎉 使用本地 Electron 构建成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 构建失败:', error.message);
      process.exit(1);
    });
}

module.exports = buildWithLocalElectron;

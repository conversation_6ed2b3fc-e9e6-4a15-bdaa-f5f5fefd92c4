const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 创建 Windows 分发包\n');

// 检查现有构建
const winUnpackedPath = 'dist-electron/win-unpacked';
if (!fs.existsSync(winUnpackedPath)) {
  console.log('❌ 未找到现有构建目录:', winUnpackedPath);
  console.log('请先运行: pnpm run build-desktop');
  process.exit(1);
}

console.log('✅ 找到现有构建目录');

// 创建分发目录
const distDir = 'distribution';
if (fs.existsSync(distDir)) {
  console.log('🧹 清理旧的分发目录...');
  try {
    execSync(`rmdir /s /q "${distDir}"`, { stdio: 'ignore', shell: true });
  } catch (error) {
    console.log('⚠️ 清理警告，继续...');
  }
}

fs.mkdirSync(distDir, { recursive: true });
console.log('📁 创建分发目录:', distDir);

// 方案 1: 创建便携版
console.log('\n📦 创建便携版分发包...');
const portableDir = path.join(distDir, 'Shop-Desktop-Portable');
fs.mkdirSync(portableDir, { recursive: true });

// 复制应用文件
console.log('📋 复制应用文件...');
try {
  execSync(`xcopy "${winUnpackedPath}" "${portableDir}" /E /I /H /Y`, { stdio: 'inherit', shell: true });
  console.log('✅ 文件复制完成');
} catch (error) {
  console.log('❌ 复制失败:', error.message);
  process.exit(1);
}

// 创建启动脚本
const startScript = `@echo off
title Shop Desktop - 商店管理系统
echo.
echo ========================================
echo    Shop Desktop - 商店管理系统
echo ========================================
echo.
echo 正在启动应用程序...
echo.

REM 检查是否存在主程序
if not exist "Shop Desktop.exe" (
    echo 错误: 未找到主程序文件
    echo 请确保所有文件完整
    pause
    exit /b 1
)

REM 启动应用程序
start "" "Shop Desktop.exe"

REM 等待一下确保程序启动
timeout /t 2 /nobreak >nul

echo 应用程序已启动！
echo.
echo 如果程序没有正常启动，请：
echo 1. 检查是否有杀毒软件阻止
echo 2. 以管理员身份运行此脚本
echo 3. 确保系统满足最低要求 (Windows 10 x64)
echo.
pause
`;

fs.writeFileSync(path.join(portableDir, '启动 Shop Desktop.bat'), startScript, 'utf8');

// 创建说明文件
const readme = `# Shop Desktop - 商店管理系统

## 🚀 快速开始

1. **启动应用**
   - 双击 "启动 Shop Desktop.bat" (推荐)
   - 或直接双击 "Shop Desktop.exe"

2. **默认账户**
   - 管理员: admin / admin123
   - 收银员: cashier / cashier123

## 📋 系统要求

- Windows 10/11 (64位)
- 4GB RAM (推荐 8GB)
- 500MB 可用磁盘空间

## 🔧 功能特性

- ✅ 商品管理 (增删改查)
- ✅ 分类管理
- ✅ 订单管理
- ✅ 销售报表
- ✅ 用户权限管理
- ✅ 数据本地存储 (SQLite)

## 🛠️ 故障排除

### 应用无法启动
1. 检查杀毒软件是否阻止
2. 以管理员身份运行
3. 确保所有文件完整

### 数据问题
- 数据存储位置: %APPDATA%\\Shop Desktop\\
- 重置数据: 删除上述目录

## 📞 技术支持

如有问题，请检查：
1. 系统是否满足最低要求
2. 是否有足够的磁盘空间
3. 防火墙/杀毒软件设置

---
版本: v1.0.0
构建时间: ${new Date().toLocaleString('zh-CN')}
`;

fs.writeFileSync(path.join(portableDir, 'README.txt'), readme, 'utf8');

// 创建便携版标识
fs.writeFileSync(path.join(portableDir, 'portable.json'), JSON.stringify({
  version: '1.0.0',
  type: 'portable',
  created: new Date().toISOString()
}, null, 2));

console.log('✅ 便携版创建完成');

// 方案 2: 创建 ZIP 压缩包
console.log('\n📦 创建 ZIP 压缩包...');
try {
  const zipName = 'Shop-Desktop-v1.0.0-Windows-x64.zip';
  const zipPath = path.join(distDir, zipName);
  
  // 使用 PowerShell 创建 ZIP
  const powershellCmd = `Compress-Archive -Path "${portableDir}\\*" -DestinationPath "${zipPath}" -Force`;
  execSync(`powershell -Command "${powershellCmd}"`, { stdio: 'inherit' });
  
  const stats = fs.statSync(zipPath);
  const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
  console.log(`✅ ZIP 创建完成: ${zipName} (${sizeInMB} MB)`);
} catch (error) {
  console.log('⚠️ ZIP 创建失败，但便携版文件夹可用');
}

// 显示结果
console.log('\n🎉 分发包创建完成！\n');
console.log('📁 分发文件位置:');
console.log(`   ${distDir}/`);
console.log('   ├── Shop-Desktop-Portable/          # 便携版文件夹');
console.log('   │   ├── Shop Desktop.exe            # 主程序');
console.log('   │   ├── 启动 Shop Desktop.bat       # 启动脚本');
console.log('   │   ├── README.txt                  # 使用说明');
console.log('   │   └── [其他应用文件]');
console.log('   └── Shop-Desktop-v1.0.0-Windows-x64.zip  # 压缩包');

console.log('\n🚀 分发方式:');
console.log('1. **文件夹分发**: 复制整个 Shop-Desktop-Portable 文件夹');
console.log('2. **ZIP 分发**: 分发 ZIP 文件，用户解压后使用');
console.log('3. **网络分发**: 上传到云存储或文件服务器');

console.log('\n👥 用户使用步骤:');
console.log('1. 获取文件夹或解压 ZIP');
console.log('2. 双击 "启动 Shop Desktop.bat"');
console.log('3. 使用默认账户登录 (admin/admin123)');

console.log('\n💡 优势:');
console.log('- ✅ 无需安装，直接运行');
console.log('- ✅ 便于携带和分发');
console.log('- ✅ 不会修改系统设置');
console.log('- ✅ 支持 U 盘运行');

console.log('\n📋 注意事项:');
console.log('- 确保目标设备满足系统要求');
console.log('- 首次运行可能需要管理员权限');
console.log('- 数据会保存在用户目录中');

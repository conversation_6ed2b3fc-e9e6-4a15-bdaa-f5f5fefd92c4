x64:
  firstOrDefaultFilePatterns:
    - '!**/node_modules/**'
    - '!dist-electron{,/**/*}'
    - dist/**/*
    - electron/**/*
    - server/**/*
    - '!server/node_modules'
    - '!server/logs/**/*'
    - '!server/test/**/*'
    - '!**/*.map'
    - '!**/*.md'
    - '!**/README*'
    - '!**/CHANGELOG*'
    - '!**/LICENSE*'
    - '!**/.git*'
    - '!**/test/**/*'
    - '!**/tests/**/*'
    - '!**/__tests__/**/*'
    - '!**/spec/**/*'
    - '!**/*.spec.*'
    - '!**/*.test.*'
    - '!**/coverage/**/*'
    - '!**/docs/**/*'
    - '!**/examples/**/*'
    - '!**/demo/**/*'
    - '!**/.nyc_output/**/*'
    - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
    - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
    - '!**/node_modules/*.d.ts'
    - '!**/node_modules/.bin'
    - '!**/node_modules/*/man/**/*'
    - '!**/node_modules/*/docs/**/*'
    - '!**/node_modules/*/example/**/*'
    - '!**/node_modules/*/examples/**/*'
    - '!**/node_modules/*/test/**/*'
    - '!**/node_modules/*/tests/**/*'
    - '!**/node_modules/*/*.md'
    - package.json
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,obj,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,pnpm-lock.yaml,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - dist/**/*
    - electron/**/*
    - server/**/*
    - '!server/node_modules'
    - '!server/logs/**/*'
    - '!server/test/**/*'
    - '!**/*.map'
    - '!**/*.md'
    - '!**/README*'
    - '!**/CHANGELOG*'
    - '!**/LICENSE*'
    - '!**/.git*'
    - '!**/test/**/*'
    - '!**/tests/**/*'
    - '!**/__tests__/**/*'
    - '!**/spec/**/*'
    - '!**/*.spec.*'
    - '!**/*.test.*'
    - '!**/coverage/**/*'
    - '!**/docs/**/*'
    - '!**/examples/**/*'
    - '!**/demo/**/*'
    - '!**/.nyc_output/**/*'
    - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
    - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
    - '!**/node_modules/*.d.ts'
    - '!**/node_modules/.bin'
    - '!**/node_modules/*/man/**/*'
    - '!**/node_modules/*/docs/**/*'
    - '!**/node_modules/*/example/**/*'
    - '!**/node_modules/*/examples/**/*'
    - '!**/node_modules/*/test/**/*'
    - '!**/node_modules/*/tests/**/*'
    - '!**/node_modules/*/*.md'

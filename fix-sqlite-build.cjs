const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 修复 SQLite 构建问题...\n');

try {
  // 1. 强制终止相关进程
  console.log('🔄 终止相关进程...');
  try {
    execSync('taskkill /f /im "Shop Desktop.exe" 2>nul', { stdio: 'ignore' });
    execSync('taskkill /f /im "electron.exe" 2>nul', { stdio: 'ignore' });
    execSync('taskkill /f /im "app-builder.exe" 2>nul', { stdio: 'ignore' });
    console.log('✅ 进程清理完成');
  } catch (error) {
    console.log('⚠️ 进程清理警告 (可忽略)');
  }

  // 等待文件释放
  console.log('⏳ 等待文件释放...');
  const start = Date.now();
  while (Date.now() - start < 3000) {
    // 等待 3 秒
  }

  // 2. 清理构建目录
  console.log('\n🧹 清理构建目录...');
  try {
    if (fs.existsSync('dist-electron')) {
      execSync('powershell -Command "Remove-Item -Recurse -Force dist-electron -ErrorAction SilentlyContinue"', { stdio: 'inherit' });
    }
    if (fs.existsSync('app')) {
      execSync('powershell -Command "Remove-Item -Recurse -Force app -ErrorAction SilentlyContinue"', { stdio: 'inherit' });
    }
    console.log('✅ 目录清理完成');
  } catch (error) {
    console.log('⚠️ 清理警告:', error.message);
  }

  // 3. 检查现有构建
  console.log('\n🔍 检查现有构建...');
  if (!fs.existsSync('dist')) {
    console.log('❌ 未找到 dist 目录，请先运行: pnpm run build');
    process.exit(1);
  }
  console.log('✅ 找到现有前端构建');

  // 4. 创建优化的 app 目录结构
  console.log('\n📁 创建优化的 app 目录结构...');
  const dirs = [
    'app',
    'app/dist',
    'app/electron',
    'app/server',
    'app/server/db',
    'app/server/routes',
    'app/server/middleware'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`  ✓ 创建 ${dir}`);
    }
  });

  // 5. 创建优化的 app/package.json (不包含 better-sqlite3)
  console.log('\n📄 创建优化的 app/package.json (临时移除 better-sqlite3)...');
  const appPackageJson = {
    "name": "shop-desktop",
    "version": "1.0.0",
    "description": "Shop Desktop - 商店桌面管理系统",
    "author": "Shop Desktop Team",
    "main": "electron/main.js",
    "type": "commonjs",
    "dependencies": {
      "body-parser": "^2.2.0",
      "cors": "^2.8.5",
      "express": "^4.21.2",
      "jsonwebtoken": "^9.0.2"
    }
  };
  
  fs.writeFileSync('app/package.json', JSON.stringify(appPackageJson, null, 2));
  console.log('✅ app/package.json 创建完成 (不包含 better-sqlite3)');

  // 6. 复制文件
  console.log('\n📦 复制文件...');
  
  // 复制前端构建文件
  console.log('  📋 复制前端文件...');
  execSync('xcopy dist app\\dist /E /I /H /Y /Q', { stdio: 'pipe' });
  console.log('  ✅ 前端文件复制完成');
  
  // 复制 Electron 文件
  console.log('  ⚡ 复制 Electron 文件...');
  const electronFiles = [
    'electron/main.js',
    'electron/server.js',
    'electron/db-init.js'
  ];
  
  electronFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join('app', file);
      fs.copyFileSync(file, destFile);
      console.log(`    ✓ ${file}`);
    }
  });
  
  // 复制服务器文件
  console.log('  🖥️ 复制服务器文件...');
  const serverFiles = [
    'server/index.js',
    'server/db/init.js',
    'server/db/schema.sql',
    'server/db/initial-data.sql',
    'server/routes/auth.js',
    'server/routes/products.js',
    'server/routes/categories.js',
    'server/routes/orders.js',
    'server/routes/reports.js',
    'server/middleware/auth.js',
    'server/middleware/logger.js'
  ];
  
  serverFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join('app', file);
      fs.copyFileSync(file, destFile);
      console.log(`    ✓ ${file}`);
    }
  });

  // 7. 手动复制 better-sqlite3
  console.log('\n📦 手动处理 better-sqlite3...');
  const rootNodeModules = 'node_modules/better-sqlite3';
  if (fs.existsSync(rootNodeModules)) {
    console.log('  📋 从根目录复制 better-sqlite3...');
    
    // 创建 app/node_modules 目录
    if (!fs.existsSync('app/node_modules')) {
      fs.mkdirSync('app/node_modules', { recursive: true });
    }
    
    // 复制 better-sqlite3
    execSync('xcopy node_modules\\better-sqlite3 app\\node_modules\\better-sqlite3 /E /I /H /Y /Q', { stdio: 'pipe' });
    console.log('  ✅ better-sqlite3 复制完成');
  }

  // 8. 在 app 目录安装其他生产依赖
  console.log('\n📦 安装其他生产依赖...');
  const originalDir = process.cwd();
  process.chdir('app');
  
  try {
    execSync('pnpm install --prod --silent', { stdio: 'inherit' });
    console.log('✅ 生产依赖安装完成');
  } catch (error) {
    console.log('⚠️ pnpm 安装失败，尝试使用 npm...');
    execSync('npm install --production --silent', { stdio: 'inherit' });
    console.log('✅ 生产依赖安装完成 (npm)');
  }
  
  process.chdir(originalDir);

  // 9. 使用特殊配置构建
  console.log('\n🔧 使用特殊配置构建...');
  
  // 备份原始配置
  const rootPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const originalBuildConfig = JSON.parse(JSON.stringify(rootPackageJson.build));
  
  // 修改为使用 app 目录，并跳过依赖重建
  rootPackageJson.build.directories = {
    ...rootPackageJson.build.directories,
    app: 'app'
  };
  
  // 简化构建配置，跳过原生模块重建
  rootPackageJson.build.files = [
    "dist/**/*",
    "electron/**/*", 
    "server/**/*",
    "node_modules/**/*",
    "!**/*.map",
    "!**/*.md"
  ];
  
  // 跳过原生模块重建
  rootPackageJson.build.npmRebuild = false;
  rootPackageJson.build.nodeGypRebuild = false;
  rootPackageJson.build.buildDependenciesFromSource = false;
  
  // 写入临时配置
  fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2));
  
  try {
    // 使用特殊参数构建
    console.log('📦 运行 electron-builder (跳过原生模块重建)...');
    execSync('npx electron-builder --win --x64 --config.electronVersion=36.4.0 --config.compression=normal --config.npmRebuild=false', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        ELECTRON_CACHE: './electron-cache',
        ELECTRON_SKIP_BINARY_DOWNLOAD: '1'
      }
    });
    console.log('✅ 构建完成');
  } catch (error) {
    console.log('⚠️ 构建失败，恢复原始配置');
    // 恢复原始配置
    rootPackageJson.build = originalBuildConfig;
    fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2));
    throw error;
  }
  
  // 恢复原始配置
  rootPackageJson.build = originalBuildConfig;
  fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2));

  // 10. 显示结果
  console.log('\n📊 构建结果:');
  if (fs.existsSync('dist-electron')) {
    const files = fs.readdirSync('dist-electron');
    files.forEach(file => {
      if (file.endsWith('.exe')) {
        const filePath = path.join('dist-electron', file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`  ✅ ${file} (${sizeInMB} MB)`);
      }
    });
  }

  // 11. 显示 app.asar 大小对比
  const appAsarPath = 'dist-electron/win-unpacked/resources/app.asar';
  if (fs.existsSync(appAsarPath)) {
    const stats = fs.statSync(appAsarPath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`  📦 app.asar: ${sizeInMB} MB (优化后)`);
    
    // 计算优化效果
    console.log('\n💡 优化效果:');
    console.log(`- 📦 app.asar 大小: ${sizeInMB} MB`);
    console.log('- ✅ 使用双 package.json 结构');
    console.log('- ✅ 只打包必需的运行时依赖');
    console.log('- ✅ 跳过了原生模块重建问题');
    console.log('- ✅ 手动处理了 better-sqlite3');
    
    // 与之前的版本对比
    const previousSize = 416.97; // 之前的版本大小
    const optimizedSize = parseFloat(sizeInMB);
    if (optimizedSize < previousSize) {
      const reduction = ((previousSize - optimizedSize) / previousSize * 100).toFixed(1);
      console.log(`- 📉 相比之前版本减少: ${reduction}%`);
    }
  }

  // 12. 验证 better-sqlite3 是否正确包含
  console.log('\n🔍 验证 better-sqlite3...');
  const sqlitePath = 'dist-electron/win-unpacked/resources/app.asar';
  if (fs.existsSync(sqlitePath)) {
    console.log('  ✅ app.asar 已创建');
  } else {
    // 检查未打包版本
    const unpackedSqlite = 'dist-electron/win-unpacked/resources/app/node_modules/better-sqlite3';
    if (fs.existsSync(unpackedSqlite)) {
      console.log('  ✅ better-sqlite3 已包含在未打包版本中');
    } else {
      console.log('  ⚠️ better-sqlite3 可能未正确包含');
    }
  }

  console.log('\n🎉 SQLite 构建修复完成！');
  console.log('\n📋 构建产物:');
  console.log('- 📁 dist-electron/win-unpacked/ - 可执行文件');
  console.log('- 📦 dist-electron/*.exe - 安装程序');
  console.log('- 🗂️ app/ - 优化后的源码结构');
  console.log('\n⚠️ 注意: 请测试数据库功能是否正常工作');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  console.log('\n💡 如果仍然失败，建议:');
  console.log('1. 使用现有便携版: distribution/Shop-Desktop-Portable/');
  console.log('2. 或者手动创建不包含数据库的版本进行测试');
  process.exit(1);
}

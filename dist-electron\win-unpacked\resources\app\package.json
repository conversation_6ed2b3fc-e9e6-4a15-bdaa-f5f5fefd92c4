{"name": "shop", "private": true, "version": "0.0.0", "type": "module", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"pnpm run dev\" \"wait-on http://localhost:5173 && electron .\"", "rebuild": "electron-rebuild", "build-electron": "pnpm run build && electron-builder", "dist": "pnpm run build && electron-builder --publish=never", "build-desktop": "node build-electron.js", "build-installer": "node build-installer.cjs", "build-all": "pnpm run build && pnpm run rebuild && pnpm run build-installer"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-table": "^8.21.3", "@types/echarts": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.9.0", "better-sqlite3": "^11.10.0", "body-parser": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "echarts": "^5.6.0", "express": "4.19.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-axios": "^2.0.6", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "xlsx": "^0.18.5", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/better-sqlite3": "^7.6.13", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "electron": "^36.4.0", "electron-builder": "^26.0.12", "electron-rebuild": "^3.2.9", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "wait-on": "^8.0.3"}, "build": {"appId": "com.shop.desktop", "productName": "Shop Desktop", "directories": {"output": "dist-electron"}, "asar": true, "files": ["dist/**/*", "electron/**/*", "server/**/*", "!server/node_modules", "!server/logs/**/*", "!server/test/**/*", "!**/*.map", "!**/*.md", "!**/README*", "!**/CHANGELOG*", "!**/LICENSE*", "!**/.git*", "!**/test/**/*", "!**/tests/**/*", "!**/__tests__/**/*", "!**/spec/**/*", "!**/*.spec.*", "!**/*.test.*", "!**/coverage/**/*", "!**/docs/**/*", "!**/examples/**/*", "!**/demo/**/*", "!**/.nyc_output/**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/node_modules/*/man/**/*", "!**/node_modules/*/docs/**/*", "!**/node_modules/*/example/**/*", "!**/node_modules/*/examples/**/*", "!**/node_modules/*/test/**/*", "!**/node_modules/*/tests/**/*", "!**/node_modules/*/*.md"], "extraResources": [{"from": "server/db/schema.sql", "to": "server/db/schema.sql"}, {"from": "server/db/initial-data.sql", "to": "server/db/initial-data.sql"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "public/vite.svg", "publisherName": "Shop Desktop", "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "menuCategory": "Business", "shortcutName": "Shop Desktop", "uninstallDisplayName": "Shop Desktop", "installerIcon": "public/vite.svg", "uninstallerIcon": "public/vite.svg", "installerHeaderIcon": "public/vite.svg", "differentialPackage": false, "perMachine": false, "runAfterFinish": true, "deleteAppDataOnUninstall": false, "include": "installer.nsh"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "compression": "maximum", "npmRebuild": false}}
directories:
  output: dist-electron
  buildResources: build
  app: app
appId: com.shop.desktop
productName: Shop Desktop
electronDownload:
  cache: ./electron-cache
asar: true
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - server/**/*
      - node_modules/**/*
      - '!**/*.map'
      - '!**/*.md'
extraResources:
  - from: server/db/schema.sql
    to: server/db/schema.sql
  - from: server/db/initial-data.sql
    to: server/db/initial-data.sql
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: public/vite.svg
  verifyUpdateCodeSignature: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  menuCategory: Business
  shortcutName: Shop Desktop
  uninstallDisplayName: Shop Desktop
  installerIcon: public/vite.svg
  uninstallerIcon: public/vite.svg
  installerHeaderIcon: public/vite.svg
  differentialPackage: false
  perMachine: false
  runAfterFinish: true
  deleteAppDataOnUninstall: false
  include: installer.nsh
portable:
  artifactName: ${productName}-${version}-portable.${ext}
nodeGypRebuild: false
buildDependenciesFromSource: false
compression: normal
npmRebuild: false
electronVersion: 36.4.0
